server:
  port: 8887

  servlet:
    context-path: /

  tomcat:
    uri-encoding: UTF-8
    threads:
      min-spare: 50
      max: 1000

# 与Spring Boot 2一样，默认情况下，大多数端点都不通过http公开，我们公开了所有端点。对于生产，您应该仔细选择要公开的端点。
management:
  #  health:
  #    elasticsearch:
  #      enabled: false
  #    datasource:
  #      enabled: false
  endpoints:
    web:
      exposure:
        include: '*'
spring:
  application:
    name: manager-api
  cache:
    type: redis
  # Redis
  redis:
    host: ************
    port: 9009
    password: Jrzh@2022
    database: 9
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: 20
        # 连接池中的最大空闲连接 默认 8
        max-idle: 10
        # 连接池中的最小空闲连接 默认 8
        min-idle: 8
  # 文件大小上传配置
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      #关闭jackson 对json做解析
      fail-on-empty-beans: false

  shardingsphere:
    datasource:
      #  数据库名称，可自定义，可以为多个，以逗号隔开，每个在这里定义的库，都要在下面定义连接属性
      names: default-datasource
      default-datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        url: *********************************************************************************************************************************************************************************************************************************************
        username: b2b
        password: Jrzh@2025_b2b
        maxActive: 20
        initialSize: 5
        maxWait: 60000
        minIdle: 5
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        #是否缓存preparedStatement，也就是PSCache。在mysql下建议关闭。 PSCache对支持游标的数据库性能提升巨大，比如说oracle。
        poolPreparedStatements: false
        #要启用PSCache，-1为关闭 必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true  可以把这个数值配置大一些，比如说100
        maxOpenPreparedStatements: -1
        #配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,log4j2
        #通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        #合并多个DruidDataSource的监控数据
        useGlobalDataSourceStat: true
        loginUsername: druid
        loginPassword: druid
    #    sharding:
    #      default-data-source-name: default-datasource
    #      #需要拆分的表，可以设置多个  在 li_order 级别即可
    #      tables:
    #        #需要进行分表的逻辑表名
    #        li_order:
    #          #实际的表结点,下面代表的是li_order_为开头的所有表，如果能确定表的范围例如按月份分表，这里的写法是data2020.li_order_$->{2020..2021}_$->{01..12}  表示例如 li_order_2020_01 li_order_2020_03 li_order_2021_01
    #          actual-data-nodes: data2020.li_order_$->{2019..2021}_$->{01..12}
    #          table-strategy:
    #            # 分表策略，根据创建日期
    #            standard:
    #              sharding-column: create_time
    #              #分表策略
    #              precise-algorithm-class-name: cn.lili.mybatis.sharding.CreateTimeShardingTableAlgorithm
    #              #范围查询实现
    #              range-algorithm-class-name: cn.lili.mybatis.sharding.CreateTimeShardingTableAlgorithm
    props:
      #是否打印逻辑SQL语句和实际SQL语句，建议调试时打印，在生产环境关闭
      sql:
        show: false

# 忽略鉴权url
ignored:
  urls:
    - /editor-app/**
    - /actuator**
    - /actuator/**
    - /MP_verify_qSyvBPhDsPdxvOhC.txt
    - /weixin/**
    - /source/**
    - /manager/passport/user/login
    - /manager/passport/user/refresh/**
    - /manager/other/elasticsearch
    - /manager/other/customWords
    - /druid/**
    - /swagger-ui.html
    - /doc.html
    - /swagger-resources/**
    - /swagger/**
    - /webjars/**
    - /v2/api-docs
    - /configuration/ui
    - /boot-admin
    - /**/*.js
    - /**/*.css
    - /**/*.png
    - /**/*.ico

# Swagger界面内容配置
swagger:
  title: lili API接口文档
  description: lili Api Documentation
  version: 1.0.0
  termsOfServiceUrl: https://pickmall.cn
  contact:
    name: lili
    url: https://pickmall.cn
    email: <EMAIL>

# Mybatis-plus
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    #缓存开启
    cache-enabled: true
    #日志
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 日志
logging:
  config: classpath:logback-spring.xml
  # 输出级别
  level:
    cn.lili: info
  #    org.hibernate: debug
  #    org.springframework: debug
  file:
    # 指定路径
    path: lili-logs
  logback:
    rollingpolicy:
      # 最大保存天数
      max-history: 7
      # 每个文件最大大小
      max-file-size: 5MB
#加密参数
jasypt:
  encryptor:
    password: lili

lili:
  system:
    isDemoSite: false
    #     脱敏级别：
    #     0：不做脱敏处理
    #     1：管理端用户手机号等信息脱敏
    #     2：商家端信息脱敏（为2时，表示管理端，商家端同时脱敏）
    sensitiveLevel: 1
  statistics:
    # 在线人数统计 X 小时。这里设置48，即统计过去48小时每小时在线人数
    onlineMember: 48
    # 当前在线人数刷新时间间隔，单位秒，设置为600，则每10分钟刷新一次
    currentOnlineUpdate: 600
  #qq lbs 申请
  lbs:
    key: 4BYBZ-7MT6S-PUAOA-6BNWL-FJUD7-UUFXT
    sk: zhNKVrJK6UPOhqIjn8AQvG37b9sz6
  #域名
  domain:
    pc: https://pc.b2b2c.pickmall.cn
    wap: https://m.b2b2c.pickmall.cn
    store: https://store.b2b2c.pickmall.cn
    admin: https://admin.b2b2c.pickmall.cn

  # jwt 细节设定
  jwt-setting:
    # token过期时间（分钟）
    tokenExpireTime: 60

  # 使用Spring @Cacheable注解失效时间
  cache:
    # 过期时间 单位秒 永久不过期设为-1
    timeout: 1500
  #多线程配置
  thread:
    corePoolSize: 5
    maxPoolSize: 50
    queueCapacity: 50
  data:
    elasticsearch:
      cluster-name: elasticsearch
      cluster-nodes: ************:9200
      index:
        number-of-replicas: 0
        number-of-shards: 3
      index-prefix: b2b
      schema: http
    #      account:
    #        username: elastic
    #        password: LiLiShopES
#    logstash:
#      server: 127.0.0.1:4560
    rocketmq:
      promotion-topic: lili_promotion_topic
      promotion-group: lili_promotion_group
      msg-ext-topic: lili_msg_topic
      msg-ext-group: lili_msg_group
      goods-topic: lili_goods_topic
      goods-group: lili_goods_group
      order-topic: lili_order_topic
      order-group: lili_order_group
      member-topic: lili_member_topic
      member-group: lili_member_group
      store-topic: lili_store_topic
      store-group: lili_store_group
      other-topic: lili_other_topic
      other-group: lili_other_group
      notice-topic: lili_notice_topic
      notice-group: lili_notice_group
      notice-send-topic: lili_send_notice_topic
      notice-send-group: lili_send_notice_group
      after-sale-topic: lili_after_sale_topic
      after-sale-group: lili_after_sale_group
rocketmq:
  name-server: ************:9876
  producer:
    group: b2b_group
    send-message-timeout: 30000

#xxl:
#  job:
#    admin:
#      addresses: http://127.0.0.1:9001/xxl-job-admin
#    executor:
#      appname: xxl-job-executor-lilishop
#      address:
#      ip:
#      port: 8891
#      logpath: ./xxl-job/executor
#      logretentiondays: 7

apiConfig:
  #上上签电子签
  bestSign:
    host: https://openapi.bestsign.info/openapi/v2
    clientSecret: 1abec8a63c7a47aca3e8e91cf6e72997
    clientId: 1637726309015663171
    developerId: 1637726309015663171
    rsaPrivateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCD6vhRyeKhC2WMz9YiwjP6l7xbvTDiIO1GQFr1NtivymepP7ILAD6WNf2fr5ZLiWrRs+2Sk4uO+/3icwiRZPtlENgkzR96JvHXt2F80qe/tNN7pErE85Vq4VoZSSg5n8I+v6VX29IS/8xCMDfi9Bvqret+osoY/DM08wmClzCRgZvD+K96mQBaFnmQpaU7HXP09WenBmaYoIOW04Ym/4gke+qbmQQfKwRZFulmjfJe98L8jAVHA7J047/t1RVkM0+20GfZhMUz672NaXCn5OQz076wVpjpSivUoYxxKGjevUdebLVAH/lyf0gykpDXf0u679tgtifO80+m0DXCCxA1AgMBAAECggEBAIPJ/bSwDUI4gYZQqKxm+cjTSDEabDitDn7NklFCLu5INpGGede1sdEdNTkQEQV7zoSbza9OyfDZ3QUbSScADIsmbdkuN9EwERIiOTmbtky2WUU/mBb1BoQViI6UYKRY75UCnyuMB25FsQk65MpOd4lXoVoNRfv6ESpH12toofPqYXBY79bHbZ6sTxDG0nBzCNASDLz5WidQqTJsRAgfK522welmtDhuNT6UvTAMFtFGhQY61IXUO1VLt38PAeyeuB7C/ykRYZm3SrqnWC6RpfionrrD/zhnMG/w5hxhDZIbX98m6cUlJK8UfIyGXm+H2nF99wq12aCOVqQI5HaE4WECgYEA405m+JU+x86+d2W+1l1gjEZ5POC3U2QlUNpO0N28ccYwm0kOdHCy+pSU4Cf7/2/sSsOYoC+l6l+M5oO7zIlC46ksHrB6D/wEO+PXB8wf7SAdTF3XAeqyudEGzYsIhDRfxooQggjx2mJSG5P86a/MjVvQG3aMif7Gv5pj+JqLlg0CgYEAlJICVmzlT1/cYQEnpZtQFaOwpeGUsjXxvmXN59hRFxOVjPeNjcve2GSRcURiGktd2jJP6Bry6J7RodalfmB7pJMSAT1HJWxribWbTDXOStUEbR/VZR/rPMtgkoftjFN3LCr/vInqkZwkctfa1DfjcGGV8nftnLV/CCWa5yPrQMkCgYBDvn4Z4AER85/4Xa5+nN3FcMt5RU/d3p7wdd+/iBY9ol+iKCfStDPjUpz8IW6NNoIp1hmKxCiirU+E9AXoc5An4adwjhAS630njEPt4vww471XoTAPNi+GCbIJWEJ1hFZ3CXbaCmhqOZtW9MgPrYE9L/vZ8373MaNyP0VENmsbkQKBgQCEsB5cV6c5C7FEBYcGNb8BhI8Ip35o5xq/ZCbQcHaDuJ7xJkqLIFAnTTJEScGgID1y2UpaTm8xaF5rPsvRIZRruFn1N2gqOTmV5NcM6mUSmkr4ttycEzTBuBXI6JM0p3qgvMpn9XjEkQJQew3aSQvc25rS0V8qfUI14XgGBSNfWQKBgDYBf3GzRDdVuoo+4/B+qKtY3UEubAlA1ggBFKyHlgat6m6fPzLgjI4iyB5QLwMTY+dfpMV6tmdm610ds0AL2m++IwZjzLWqzBnIMo+1y19hMI92DK05rSZEWvhKZireVtNONhwhjLwUnaB2gXQ2AMPmyA2Rry1B5id62vQht2NB
    hostSuffix: /openapi/v2
    account: <EMAIL>
    platAccount: 1580081262574469121

huawei:
  auth:
    ak: N70HQ9BBHKZJC1O7Q6M7
    sk: FgJawGvrlqTgS2Zl9HjgxumegJM0i7q8HkAVhzjd