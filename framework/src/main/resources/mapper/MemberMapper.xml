<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.lili.modules.member.mapper.MemberMapper">

    <!-- 会员信息分页查询 -->
    <select id="pageByMemberVO" resultType="cn.lili.modules.member.entity.vo.MemberVO">
        SELECT 
            m.id,
            m.username,
            m.password,
            m.nick_name,
            m.sex,
            m.birthday,
            m.region_id,
            m.region,
            m.mobile,
            m.point,
            m.total_point,
            m.face,
            m.disabled,
            m.have_store,
            m.store_id,
            m.client_enum,
            m.last_login_date,
            m.grade_id,
            m.experience,
            m.status,
            m.connect_sys_type,
            m.finance_account,
            m.create_time,
            m.update_time,
            m.create_by,
            m.update_by,
            m.delete_flag,
            ci.corporation_name,
            ci.corp_name,
            ci.business_licence_number
        FROM li_member m 
        LEFT JOIN li_customer_info ci ON m.id = ci.customer_id
        ${ew.customSqlSegment}
    </select>

</mapper>
