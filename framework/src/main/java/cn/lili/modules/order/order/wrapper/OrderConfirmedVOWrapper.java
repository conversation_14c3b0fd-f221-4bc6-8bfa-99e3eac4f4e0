package cn.lili.modules.order.order.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.lili.modules.jrzh_bases.BaseEntityWrapper;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.vo.OrderConfirmedVO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年05月20日11:17
 */
public class OrderConfirmedVOWrapper extends BaseEntityWrapper<OrderItem, OrderConfirmedVO> {

    public static OrderConfirmedVOWrapper build() {
        return new OrderConfirmedVOWrapper();
    }

    @Override
    public OrderConfirmedVO entityVO(OrderItem entity) {
        OrderConfirmedVO vo = Objects.requireNonNull(BeanUtil.copyProperties(entity, OrderConfirmedVO.class));
        vo.setGoodsNum(entity.getNum());
        return vo;
    }
}
