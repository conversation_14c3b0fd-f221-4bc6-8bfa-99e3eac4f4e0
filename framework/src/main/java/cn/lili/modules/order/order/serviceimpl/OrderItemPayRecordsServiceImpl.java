package cn.lili.modules.order.order.serviceimpl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.dto.OrderFinancingPaidNotify;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dos.OrderItemPayRecords;
import cn.lili.modules.order.order.entity.dto.OrderItemPayRecordsParams;
import cn.lili.modules.order.order.entity.dto.UpdatePayRecordsDto;
import cn.lili.modules.order.order.entity.enums.PayRecordsPaymentMethodEnum;
import cn.lili.modules.order.order.entity.enums.PayRecordsPaymentStatusEnum;
import cn.lili.modules.order.order.entity.enums.PayRecordsTypeEnum;
import cn.lili.modules.order.order.entity.vo.OrderItemPayRecordsVo;
import cn.lili.modules.order.order.entity.vo.OrderItemPayableVo;
import cn.lili.modules.order.order.mapper.OrderItemPayRecordsMapper;
import cn.lili.modules.order.order.service.OrderItemPayRecordsService;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月05日16:50
 */
@Service
@RequiredArgsConstructor
public class OrderItemPayRecordsServiceImpl extends ServiceImpl<OrderItemPayRecordsMapper, OrderItemPayRecords> implements OrderItemPayRecordsService {

    private final OrderItemPayRecordsMapper orderItemPayRecordsMapper;
    private final OrderItemService orderItemService;

    /**
     * 子订单支付记录
     * @param orderItem
     * @param fileUrls
     * @return
     */
    @Override
    public Boolean orderItemPayment(OrderItem orderItem, List<String> fileUrls) {
        AuthUser tokenUser = UserContext.getCurrentUser();
        String memberId = tokenUser.getId();
        List<OrderItemPayRecords> list = new ArrayList<>();
        String parentIdStr = IdWorker.getIdStr();
        OrderItemPayableVo orderItemPayableVo = orderItemService.payableByItemSn(orderItem.getSn());
        OrderItemPayRecords parentRecords = new OrderItemPayRecords(orderItem, null, memberId, PayRecordsTypeEnum.PAYMENT_RECORD, PayRecordsPaymentMethodEnum.PAYMENT_VOUCHER);
        parentRecords.setPayableAmount(orderItemPayableVo.getPayableAmount());
        parentRecords.setId(parentIdStr);
        for (String fileUrl : fileUrls) {
            OrderItemPayRecords orderItemPayRecords = new OrderItemPayRecords(orderItem, fileUrl, memberId, PayRecordsTypeEnum.PAYMENT_VOUCHER, PayRecordsPaymentMethodEnum.PAYMENT_VOUCHER);
            orderItemPayRecords.setParentId(parentIdStr);
            list.add(orderItemPayRecords);
        }
        list.add(parentRecords);
        return this.saveBatch(list);
    }
    /**
     * 子订单融资支付记录
     * @param orderItem
     * @param orderFinancingPaidNotify
     * @param memberId
     * @return
     */
    @Override
    public Boolean orderItemFinancingPayment(OrderItem orderItem, OrderFinancingPaidNotify orderFinancingPaidNotify, String memberId) {
        OrderItemPayRecords orderItemPayRecords = new OrderItemPayRecords(orderItem, null, memberId, PayRecordsTypeEnum.PAYMENT_RECORD, PayRecordsPaymentMethodEnum.FINANCING);
        orderItemPayRecords.setPaymentStatus(PayRecordsPaymentStatusEnum.PAYMENT_UN_CONFIRM.name());
        orderItemPayRecords.setPaidAmount(orderFinancingPaidNotify.getPaidAmount());
        OrderItemPayableVo orderItemPayableVo = orderItemService.payableByItemSn(orderItem.getSn());
        orderItemPayRecords.setPayableAmount(orderItemPayableVo.getPayableAmount());
        orderItemPayRecords.setBackPaidSn(orderFinancingPaidNotify.getFinanceNo());
        return this.save(orderItemPayRecords);
    }

    /**
     * 更新支付状态为确认或拒绝
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePayRecords(UpdatePayRecordsDto dto) {
        AuthUser currentUser = UserContext.getCurrentUser();
        // 找到支付凭证
        OrderItemPayRecords parent = this.getOne(Wrappers.<OrderItemPayRecords>lambdaQuery()
                .eq(OrderItemPayRecords::getId, dto.getPayRecordsId())
                .eq(OrderItemPayRecords::getPaymentStatus, PayRecordsPaymentStatusEnum.PAYMENT_UN_CONFIRM.name())
        );
        if (ObjectUtil.isEmpty(parent)) {
            throw new ServiceException("不存在未确认的支付记录");
        }
        // 如果是确认,需填充金额进去
        if (PayRecordsPaymentStatusEnum.PAYMENT_CONFIRM.name().equals(dto.getPaymentStatus())) {
            parent.setPaidAmount(dto.getAmount());
        }
        parent.setOperateMemberId(currentUser.getId());
        parent.setOperateMemberName(currentUser.getUsername());
        parent.setOperateTime(new Date());
        parent.setPaymentStatus(dto.getPaymentStatus());
        // 查询该支付记录下的支付凭证,将其状态变更为已确认
        String orderItemSn = parent.getOrderItemSn();
        List<OrderItemPayRecords> list = this.list(Wrappers.<OrderItemPayRecords>lambdaQuery()
                .eq(OrderItemPayRecords::getParentId, parent.getId())
                .eq(OrderItemPayRecords::getPaymentStatus, PayRecordsPaymentStatusEnum.PAYMENT_UN_CONFIRM.name())
        );
        if (CollectionUtil.isNotEmpty(list)) {
            for (OrderItemPayRecords orderItemPayRecords : list) {
                orderItemPayRecords.setPaymentStatus(dto.getPaymentStatus());
            }
            list.add(parent);
            return this.updateBatchById(list);
        }
        return this.updateById(parent);
    }

    /**
     * 根据子订单编号获取所有的支付记录
     * @param orderItemSns
     * @param typeEnum
     * @return
     */
    @Override
    public List<OrderItemPayRecords> getListByItemSn(List<String> orderItemSns, PayRecordsTypeEnum typeEnum) {
        return this.list(Wrappers.<OrderItemPayRecords>lambdaQuery()
                .in(OrderItemPayRecords::getOrderItemSn, orderItemSns)
                .eq(OrderItemPayRecords::getType, typeEnum.name())
                .eq(OrderItemPayRecords::getDeleteFlag, false)
        );
    }

    /**
     * 分页
     * @param params
     * @return
     */
    @Override
    public IPage<OrderItemPayRecordsVo> pageByParams(OrderItemPayRecordsParams params) {
        QueryWrapper<OrderItemPayRecordsVo> wrapper = params.queryWrapper();
        wrapper.orderByDesc("oipr.id");
        wrapper.eq("oipr.type", PayRecordsTypeEnum.PAYMENT_RECORD.name());
        IPage<OrderItemPayRecordsVo> voIPage = baseMapper.pageByParams(PageUtil.initPage(params), wrapper);
//        List<OrderItemPayRecordsVo> records = voIPage.getRecords();
//        if (CollectionUtil.isEmpty(records)) {
//            return voIPage;
//        }
//        List<String> parentIdList = CollStreamUtil.toList(records, OrderItemPayRecordsVo::getId);
//        List<OrderItemPayRecords> paymentVoucherList = this.list(Wrappers.<OrderItemPayRecords>lambdaQuery()
//                .in(OrderItemPayRecords::getParentId, parentIdList)
//                .eq(OrderItemPayRecords::getDeleteFlag, false)
//        );
//        // 如果没有支付凭证数据直接返回
//        if (CollectionUtil.isEmpty(paymentVoucherList)) {
//            return voIPage;
//        }
//        Map<String, List<OrderItemPayRecords>> parentIdMap = CollStreamUtil.groupBy(paymentVoucherList, OrderItemPayRecords::getParentId, Collectors.toList());
//        for (OrderItemPayRecordsVo recordsVo : records) {
//            if (parentIdMap.containsKey(recordsVo.getId())) {
//                recordsVo.setPaymentVoucherList(parentIdMap.get(recordsVo.getId()));
//            }
//        }
        return voIPage;
    }


    /**
     * 详情
     * @param id
     * @return
     */
    @Override
    public OrderItemPayRecordsVo detail(String id) {
        OrderItemPayRecordsParams params = new OrderItemPayRecordsParams();
        params.setId(id);
        OrderItemPayRecordsVo vo = baseMapper.oneByParams(params.queryWrapper());
        List<OrderItemPayRecords> paymentVoucherList = this.list(Wrappers.<OrderItemPayRecords>lambdaQuery()
                .eq(OrderItemPayRecords::getParentId, id)
                .eq(OrderItemPayRecords::getDeleteFlag, false)
        );
        List<String> paymentVoucherUrlList = CollStreamUtil.toList(paymentVoucherList, OrderItemPayRecords::getPaymentVoucherUrl);
        vo.setPaymentVoucherUrlList(paymentVoucherUrlList);
        return vo;
    }

}
