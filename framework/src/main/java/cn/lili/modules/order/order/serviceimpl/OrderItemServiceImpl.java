package cn.lili.modules.order.order.serviceimpl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.StringUtils;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.entity.enums.SettlementModelEnum;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dto.OrderItemOperationDTO;
import cn.lili.modules.order.order.entity.dto.PendingInvoicingPageParams;
import cn.lili.modules.order.order.entity.enums.*;
import cn.lili.modules.order.order.entity.vo.OrderConfirmedVO;
import cn.lili.modules.order.order.entity.vo.OrderItemPayableVo;
import cn.lili.modules.order.order.entity.vo.OrderItemVO;
import cn.lili.modules.order.order.mapper.OrderItemMapper;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.wrapper.OrderConfirmedVOWrapper;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 子订单业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:38 下午
 */
@Service
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem> implements OrderItemService {

    @Override
    public void updateCommentStatus(String orderItemSn, CommentStatusEnum commentStatusEnum) {
        LambdaUpdateWrapper<OrderItem> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.set(OrderItem::getCommentStatus, commentStatusEnum.name());
        lambdaUpdateWrapper.eq(OrderItem::getSn, orderItemSn);
        this.update(lambdaUpdateWrapper);
    }

    @Override
    public void updateAfterSaleStatus(String orderItemSn, OrderItemAfterSaleStatusEnum orderItemAfterSaleStatusEnum) {
        LambdaUpdateWrapper<OrderItem> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.set(OrderItem::getAfterSaleStatus, orderItemAfterSaleStatusEnum.name());
        lambdaUpdateWrapper.eq(OrderItem::getSn, orderItemSn);
        this.update(lambdaUpdateWrapper);
    }

    @Override
    public void updateByAfterSale(OrderItem orderItem) {
        LambdaUpdateWrapper<OrderItem> lambdaUpdateWrapper = new LambdaUpdateWrapper<OrderItem>()
                .eq(OrderItem::getSn, orderItem.getSn())
                .set(OrderItem::getIsRefund, orderItem.getIsRefund())
                .set(OrderItem::getRefundPrice, orderItem.getRefundPrice());
        this.update(lambdaUpdateWrapper);
    }

    /**
     * 更新订单可投诉状态
     *
     * @param orderSn            订单sn
     * @param skuId              商品skuId
     * @param complainId         订单交易投诉ID
     * @param complainStatusEnum 修改状态
     */
    @Override
    public void updateOrderItemsComplainStatus(String orderSn, String skuId, String complainId, OrderComplaintStatusEnum complainStatusEnum) {
        LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItem::getOrderSn, orderSn).eq(OrderItem::getSkuId, skuId);
        OrderItem orderItem = getOne(queryWrapper);
        if (orderItem == null) {
            throw new ServiceException(ResultCode.ORDER_ITEM_NOT_EXIST);
        }
        orderItem.setComplainId(complainId);
        orderItem.setComplainStatus(complainStatusEnum.name());
        updateById(orderItem);
    }

    @Override
    public OrderItem getBySn(String sn) {
        LambdaQueryWrapper<OrderItem> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(OrderItem::getSn, sn);
        return this.getOne(lambdaQueryWrapper);
    }

    @Override
    public List<OrderItem> getByOrderSn(String orderSn) {
        LambdaQueryWrapper<OrderItem> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(OrderItem::getOrderSn, orderSn);
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 根据订单编号数组获取子订单列表
     * @param orderSnList
     * @return
     */
    @Override
    public List<OrderItem> getByOrderSnList(List<String> orderSnList) {
        LambdaQueryWrapper<OrderItem> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(OrderItem::getOrderSn, orderSnList);
        return this.list(lambdaQueryWrapper);
    }

    @Override
    public OrderItem getByOrderSnAndSkuId(String orderSn, String skuId) {
        return this.getOne(new LambdaQueryWrapper<OrderItem>()
                .eq(OrderItem::getOrderSn, orderSn)
                .eq(OrderItem::getSkuId, skuId));
    }

    @Override
    public List<OrderItem> waitOperationOrderItem(OrderItemOperationDTO dto) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.le("o.complete_time", dto.getReceiveTime());
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(dto.getCommentStatus()), "oi.comment_status", dto.getCommentStatus());
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(dto.getAfterSaleStatus()), "oi.after_sale_status", dto.getAfterSaleStatus());
        queryWrapper.eq(CharSequenceUtil.isNotEmpty(dto.getComplainStatus()), "oi.complain_status", dto.getComplainStatus());
        return this.baseMapper.waitOperationOrderItem(queryWrapper);
    }


    @Override
    public void expiredAfterSaleStatus(DateTime expiredTime) {
        this.baseMapper.expiredAfterSaleStatus(expiredTime);
        this.baseMapper.expiredAfterSaleStatusExecuteByAfterSale(expiredTime);
    }

    /**
     * 获取子订单应付金额等
     * @param orderItemSn
     * @return
     */
    @Override
    public OrderItemPayableVo payableByItemSn(String orderItemSn) {
        OrderItem orderItem = this.getBySn(orderItemSn);
        if (ObjectUtil.isEmpty(orderItem)) {
            throw new ServiceException(ResultCode.ORDER_ITEM_NOT_EXIST);
        }
        String itemPaymentStatus = orderItem.getItemPaymentStatus();
        String settlementModel = orderItem.getSettlementModel();
        BigDecimal payableAmount = BigDecimal.ZERO;
        List<String> unChangeConfirmStatus = Arrays.asList(OrderItemChangeConfirmStatus.UN_CONFIRM.name(), OrderItemChangeConfirmStatus.REFUSE.name());
        // 如果是全额, 直接算总额即可
        if (SettlementModelEnum.FULL_PAYMENT.name().equals(settlementModel)) {
            payableAmount = orderItem.getChangeFlowPrice().add(BigDecimal.valueOf(orderItem.getPriceDetailDTO().getFreightPrice())).subtract(orderItem.getPaidAmount());
            if (unChangeConfirmStatus.contains(orderItem.getChangeConfirmStatus())) {
                payableAmount = BigDecimal.valueOf(orderItem.getFlowPrice()).subtract(orderItem.getPaidAmount());
            }
        }
        List<String> oneStatus = Arrays.asList(
                OrderItemPaymentStatusEnum.FIRST_PAYMENT_UN_CONFIRM.name(),
                OrderItemPaymentStatusEnum.FIRST_PAYMENT_REFUSE.name()
        );
        List<String> twoStatus = Arrays.asList(
                OrderItemPaymentStatusEnum.FIRST_PAYMENT_CONFIRM.name(),
                OrderItemPaymentStatusEnum.BALANCE_PAYMENT_UN_CONFIRM.name(),
                OrderItemPaymentStatusEnum.BALANCE_PAYMENT_REFUSE.name()
        );
        // 如果是定金, 一次付款为总额乘以比例, 二次付款为应付总额减去已付的定金
        if (SettlementModelEnum.DEPOSIT_SEND_OUT_GOODS.name().equals(settlementModel)) {
            // 为空说明是第一次付款, 所以直接按比例计算即可
            if (StringUtils.isEmpty(itemPaymentStatus) || oneStatus.contains(itemPaymentStatus)) {
                payableAmount = BigDecimal.valueOf(orderItem.getFlowPrice())
                        .multiply(orderItem.getEarnestMoneyRatio())
                        .divide(new BigDecimal("100")).subtract(orderItem.getPaidAmount());
            } else if (twoStatus.contains(itemPaymentStatus)){
                payableAmount = orderItem.getChangeFlowPrice().add(BigDecimal.valueOf(orderItem.getPriceDetailDTO().getFreightPrice())).subtract(orderItem.getPaidAmount());
                if (unChangeConfirmStatus.contains(orderItem.getChangeConfirmStatus())) {
                    payableAmount = BigDecimal.valueOf(orderItem.getFlowPrice()).subtract(orderItem.getPaidAmount());
                }
            } else {
                payableAmount = BigDecimal.ZERO;
            }
        }
        return OrderItemPayableVo.builder()
                .itemPaymentStatus(itemPaymentStatus)
                .settlementModel(settlementModel)
                .payableAmount(payableAmount)
                .paidAmount(orderItem.getPaidAmount())
                .orderItemAmount(BigDecimal.valueOf(orderItem.getFlowPrice()))
                .build();
    }

    /**
     * 待开票子订单列表
     * @param pageParams
     * @return
     */
    @Override
    public IPage<OrderItemVO> pendingInvoicingPage(PendingInvoicingPageParams pageParams) {
        IPage<OrderItemVO> iPageVo = this.baseMapper.pendingInvoicingPage(PageUtil.initPage(pageParams), pageParams.queryWrapper());
        return iPageVo;
    }

    /**
     * 变更子订单申请开票状态
     * @param orderItemSnList
     * @param applyInvoicingStatusEnum
     * @return
     */
    @Override
    public Boolean updateApplyInvoicingStatus(List<String> orderItemSnList, ApplyInvoicingStatusEnum applyInvoicingStatusEnum) {
        return this.update(Wrappers.<OrderItem>lambdaUpdate()
                .in(OrderItem::getSn, orderItemSnList)
                .set(OrderItem::getApplyInvoicingStatus, applyInvoicingStatusEnum.name())
        );
    }

    @Override
    public IPage<OrderConfirmedVO> orderConfirmedPage(PageVO pageVO) {
        Page<OrderItem> page = this.page(PageUtil.initPage(pageVO), Wrappers.<OrderItem>lambdaQuery()
                .eq(OrderItem::getItemPaymentStatus, OrderItemPaymentStatusEnum.BALANCE_PAYMENT_CONFIRM)
                .orderByDesc(OrderItem::getCreateTime)
        );
        return OrderConfirmedVOWrapper.build().pageVO(page);
    }

}