package cn.lili.modules.jrzh_other.bestsign.connector;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.HttpUtils;

import java.io.*;

import cn.lili.modules.file.plugin.FilePluginFactory;
import cn.lili.modules.jrzh_bases.BladeFile;
import cn.lili.modules.jrzh_bases.HttpClientSender;
import cn.lili.modules.jrzh_other.bestsign.config.BestSignConfig;
import cn.lili.modules.jrzh_other.bestsign.constant.BestSignConstant;
import cn.lili.modules.jrzh_other.bestsign.constant.BestSignPathEnum;
import cn.lili.modules.jrzh_other.bestsign.dto.BestSignPathVariable;
import cn.lili.modules.jrzh_other.bestsign.dto.SSQResult;
import cn.lili.modules.jrzh_other.core.service.IOthersApiService;
import cn.lili.modules.jrzh_other.core.utils.RSAUtils;
import cn.lili.modules.jrzh_other.core_api.constant.OtherApiTypeEnum;
import cn.lili.modules.jrzh_other.core_api.dto.ApiParamDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 第三方接口连接器
 */
@Component
@RequiredArgsConstructor
public class BestSignConnector {
    private final static String CLIENT_ID = "clientId";
    private final static String CLIENT_SECRET = "clientSecret";
    private final static String DEVELOPER_ID = "developerId";
    private final static String RSA_PRIVATE_KEY = "rsaPrivateKey";
    private final static String HOST_SUFFIX = "hostSuffix";
    private final static String HOST = "host";
    private final static String RTICK = "rtick";
    private final static String SIGN_TYPE = "signType";
    private final static String REQUEST_POST = "POST";
    private final static String REQUEST_GET = "GET";
    private final static int RESULT_SUCCESS_CODE = 0;
    private final IOthersApiService othersApiService;
    private final FilePluginFactory filePluginFactory;


    private final BestSignConfig bestSignConfig;
//	private final ILogInterfaceService logInterfaceService;
//

    /**
     * 获取公共配置
     *
     * @return
     */

    private BestSignConfig getBestSignCommon() {
        //获取共有变量
//        ApiParamDTO apiParams = othersApiService.getSingleParamByTypeCode(OtherApiTypeEnum.ELEC_SIGN.getCode());
//        Map<String, String> paramMap = apiParams.getParamMap();
//        BestSignConfig bestSignConfig = BestSignConfig.builder().clientId(paramMap.get(CLIENT_ID))
//                .clientSecret(paramMap.get(CLIENT_SECRET))
//                .rsaPrivateKey(paramMap.get(RSA_PRIVATE_KEY))
//                .developerId(paramMap.get(DEVELOPER_ID))
//                .host(paramMap.get(HOST))
//                .account(paramMap.get("account"))
//                .hostSuffix(paramMap.get(HOST_SUFFIX)).build();
//        return bestSignConfig;
        return null;
    }
//

    /**
     * 获取上上签签名
     *
     * @param url
     * @param requestMethod
     * @param requestData
     * @param timeStamp
     * @param bestSignConfig
     * @return
     */
    private String getSign(String url, String requestMethod, String requestData, String timeStamp, BestSignConfig bestSignConfig) {
        String content = null;
        if (REQUEST_GET.equals(requestMethod)) {
            content = getMethodFormatContent(url, requestData, timeStamp, bestSignConfig);
        } else {
            content = String.format(BestSignConstant.BEST_POST_URI, bestSignConfig.getDeveloperId(), timeStamp, BestSignConstant.SIGNATURE_TYPE, url, getRequestMd5(ObjectUtil.isEmpty(requestData) ? "" : requestData));
        }
        try {
            return RSAUtils.signRSA(content, bestSignConfig.getRsaPrivateKey());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "签名错误";
    }
//

    /**
     * 按上上签格式拼接明文 规则：1、计算的参数进行字典序排序 2、把请求path附加到字符串后面
     *
     * @param url            实际请求路径
     * @param requestData    请求参数
     * @param timeStamp      时间戳
     * @param bestSignConfig 配置
     * @return
     */
    private String getMethodFormatContent(String url, String requestData, String timeStamp, BestSignConfig bestSignConfig) {
        Map<String, Object> map = JSONObject.parseObject(requestData, Map.class);
        map.put(DEVELOPER_ID, bestSignConfig.getDeveloperId());
        map.put(RTICK, timeStamp);
        map.put(SIGN_TYPE, BestSignConstant.SIGNATURE_TYPE);
        //对参数进行排序
        Map<String, Object> sortMap = sortMapByKey(map);
        StringBuilder content = new StringBuilder();
        sortMap.forEach((k, v) -> {
            content.append(k).append("=").append(v);
        });
        content.append(url);
        return content.toString();
    }

    /**
     * 根据map的key进行字典升序排序
     *
     * @param map
     * @return map
     */
    public static Map<String, Object> sortMapByKey(Map<String, Object> map) {
        Map<String, Object> treemap = new TreeMap<String, Object>(map);
        List<Map.Entry<String, Object>> list = new ArrayList<Map.Entry<String, Object>>(treemap.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Object>>() {
            public int compare(Map.Entry<String, Object> o1, Map.Entry<String, Object> o2) {
                return o1.getKey().compareTo(o2.getKey());
            }
        });
        return treemap;
    }
//
//    /**
//     * 获取当前系统的时间戳
//     *
//     * @return
//     */

    /**
     * 获取当前的时间戳参数
     *
     * @return
     */
    public static String getBestSignTimeStamp() {
        long timestamp = System.currentTimeMillis();
        SecureRandom secureRandom = new SecureRandom();
        int randomNumberInRange = secureRandom.nextInt(8) + 1;
        int rnd = randomNumberInRange * 1000;
        String rtick = timestamp + "" + rnd;
        return rtick;
    }
//

    /***
     * 上上签post请求通用
     * @param url 请求地址
     * @param requestData 请求参数
     * @return
     */
    public SSQResult post(String url, String requestData) {
        return post(url, requestData, true);
    }

    /***
     * 上上签post请求通用
     * @param url
     * @param requestData
     * @return
     */
    @SneakyThrows
    public SSQResult post(String url, String requestData, Boolean dealError) {
//        BestSignConfig bestSignConfig = getBestSignCommon();
        String host = bestSignConfig.getHost();
        if (url.contains(BestSignPathVariable.CONTRACT_PATH.getVariable())) {
            url = url.replace(BestSignPathVariable.CONTRACT_PATH.getVariable(), JSONObject.parseObject(requestData).getString(BestSignPathVariable.CONTRACT_PATH.getRequestValue()));
            Map<String, String> params = JSONObject.parseObject(requestData, new TypeReference<Map<String, String>>() {
            });
            params.remove(BestSignPathVariable.CONTRACT_PATH.getRequestValue());
            requestData = JSONObject.toJSONString(params);
        }

        String bestSignTimeStamp = getBestSignTimeStamp();
        String reallyUrl = host + url;
        String requestPath;
        try {
            requestPath = new URL(reallyUrl).getPath();
        } catch (MalformedURLException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        String sign = getSign(requestPath, REQUEST_POST, requestData, bestSignTimeStamp, bestSignConfig);
        String urlSignParams = String.format(BestSignConstant.URL_FORMAT, bestSignConfig.getDeveloperId(), bestSignTimeStamp, sign);
        String ssqResult = null;
        //获取头部参数 发送请求
        String key = BestSignPathEnum.getDescByPath(url);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start(key);
        String requestUrl = host + url + urlSignParams;

        ssqResult = HttpUtil.post(requestUrl, requestData);
//        ssqResult = HttpClientSender.sendHttpPost(host, url, urlSignParams, requestData);
        stopWatch.stop();
        System.out.println(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        SSQResult returnResult = JSONUtil.toBean(ssqResult, SSQResult.class, true);
        //日志记录
//        saveLog(ssqResult, url, requestData, getSuccess(returnResult));
        if (dealError) {
            dealErrorResult(returnResult);
        }
        return returnResult;
    }
//

    /***
     * 上上签下载请求通用
     * @param url  上上签下载URL
     * @return
     */
    @SneakyThrows
    public BladeFile getToFile(String url, Map<String, Object> paramObj, String fileName) {
//        BestSignConfig bestSignConfig = getBestSignCommon();
        String host = bestSignConfig.getHost();
        String bestSignTimeStamp = getBestSignTimeStamp();
        String reallyUrl = host + url;
        String requestPath;

        requestPath = new URL(reallyUrl).getPath();
        String paramStr = JSONUtil.toJsonStr(paramObj);
        String sign = getSign(requestPath, REQUEST_GET, paramStr, bestSignTimeStamp, bestSignConfig);
        String urlSignParams = String.format(BestSignConstant.URL_FORMAT, bestSignConfig.getDeveloperId(), bestSignTimeStamp, sign);
        StringBuilder urlParams = new StringBuilder();
        paramObj.forEach((k, v) -> urlParams.append("&").append(k).append("=").append(v.toString()));
        byte[] ssqResult = null;
        String result;
        //获取头部参数 发送请求
        BladeFile bladeFile = new BladeFile();
        Integer success = 0;
        try {
//            String requestUrl = host + url + urlSignParams + urlParams;

//            String ssqResult = HttpUtil.get(requestUrl);
//            SSQResult returnResult = JSONUtil.toBean(ssqResult, SSQResult.class, true);
//            String newUrl = JSONUtil.parseObj(returnResult.getData()).getStr("contractId");
            ssqResult = HttpClientSender.sendHttpGet(host, url, urlSignParams + urlParams);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(ssqResult);
            result = filePluginFactory.filePlugin().inputStreamUpload(inputStream, fileName);
//            File tempFile = File.createTempFile(fileName, ".pdf");
//            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
//                fos.write(ssqResult);
//            }
//            result=filePluginFactory.filePlugin().pathUpload(tempFile.getCanonicalPath(), fileName);
            bladeFile.setLink(result);
            success = 1;
        } finally {
            //日志记录//TODO未做日志记录
//            saveLog(bladeFile.toString(), url, paramStr, success);
        }
        return bladeFile;
    }

    /**
     * 是否调用成功
     * 0 失败 1成功
     *
     * @param returnResult
     * @return
     */
    public Integer getSuccess(SSQResult returnResult) {
        if (returnResult.getErrno() == null) {
            return 1;
        }
        if (RESULT_SUCCESS_CODE == returnResult.getErrno()) {
            return 1;
        }
        return 0;
    }
//
//    public static Boolean isSuccess(SSQResult result) {
//        return RESULT_SUCCESS_CODE == result.getErrno();
//    }
//
//    /**
//     * 日志记录
//     *
//     * @param ssqResult   上上签返回结果
//     * @param url         请求地址
//     * @param requestData 请求参数
//     * @param result      调用结果 1成功 2失败
//     */
//    @Async
//    public void saveLog(String ssqResult, String url, String requestData, Integer result) {
//        String pathDesc = BestSignPathEnum.getDescByPath(url);
//		LogInterface logInterface = LogInterface.builder()
//				.caller(1)
//				.creditCompanyName("精锐纵横网络有限公司")
//				.interfaceParty("上上签")
//				.name(pathDesc)
//				.requestInfo(requestData)
//				.requestUrl(url)
//				.responseInfo(ssqResult)
//				.result(result)
//				.build();
//		logInterfaceService.saveLog(logInterface);
//    }

    private void dealErrorResult(SSQResult ssqResult) {
        Integer success = getSuccess(ssqResult);
        if (success == 0) {
            throw new ServiceException(ssqResult.getErrmsg());
        }
    }
//
//    /***
//     * 上上签get请求通用
//     * @param url 请求地址
//     * @param requestData 请求参数
//     * @return
//     */
//    public SSQResult get(String url, String requestData) {
//        return get(url, requestData, false);
//    }
//
//    /***
//     * 上上签get请求通用
//     * @param url
//     * @param requestData
//     * @return
//     */
//    @SneakyThrows
//    public SSQResult get(String url, String requestData, Boolean dealError) {
//        BestSignConfig bestSignConfig = getBestSignCommon();
//        //获取公共配置
//        String host = bestSignConfig.getHost();
//        if (url.contains(BestSignPathVariable.CONTRACT_PATH.getVariable())) {
//            url = url.replace(BestSignPathVariable.CONTRACT_PATH.getVariable(), JSONObject.parseObject(requestData).getString(BestSignPathVariable.CONTRACT_PATH.getRequestValue()));
//            Map<String, String> params = JSONObject.parseObject(requestData, new TypeReference<Map<String, String>>() {
//            });
//            params.remove(BestSignPathVariable.CONTRACT_PATH.getRequestValue());
//            requestData = JSONObject.toJSONString(params);
//        }
//        String bestSignTimeStamp = getBestSignTimeStamp();
//        String reallyUrl = host + url;
//        String requestPath;
//        try {
//            requestPath = new URL(reallyUrl).getPath();
//        } catch (MalformedURLException e) {
//            throw new RuntimeException(e.getMessage(), e);
//        }
//        String sign = getSign(requestPath, REQUEST_GET, requestData, bestSignTimeStamp, bestSignConfig);
//        String urlParams = String.format(BestSignConstant.URL_FORMAT, bestSignConfig.getDeveloperId(), bestSignTimeStamp, sign);
//
//        String ssqResult = HttpUtil.get(reallyUrl + urlParams, null, JSONObject.parseObject(requestData, Map.class));
//        //获取头部参数 发送请求
//        SSQResult returnResult = JSONUtil.toBean(ssqResult, SSQResult.class, true);
//        //日志记录
//        saveLog(ssqResult, url, requestData, getSuccess(returnResult));
//        if (dealError) {
//            dealErrorResult(returnResult);
//        }
//        return returnResult;
//    }
//    /**
//     * get请求统一处理
//     */
//

    /**
     * 获取request body 的MD5
     *
     * @param requestBody
     * @return
     */
    private static String getRequestMd5(final String requestBody) {
        byte[] data;

        String newRequestBody = convertToUtf8(requestBody);
        try {
            data = newRequestBody.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        return md5(data);
    }
//

    /**
     * 转换字符集到utf8
     *
     * @param src
     * @return
     */
    private static String convertToUtf8(String src) {
        if (src == null || src.length() == 0) {
            return src;
        }
        if ("UTF-8".equalsIgnoreCase(Charset.defaultCharset().name())) {
            return src;
        }

        byte[] srcData = src.getBytes();
        try {
            return new String(srcData, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }
//

    /**
     * md5
     *
     * @param data
     * @return
     */
    public static String md5(byte[] data) {
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        byte[] btInput = data;
        // 获得MD5摘要算法的 MessageDigest 对象
        MessageDigest mdInst;
        try {
            mdInst = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        // 使用指定的字节更新摘要
        mdInst.update(btInput);
        // 获得密文
        byte[] md = mdInst.digest();
        // 把密文转换成十六进制的字符串形式
        int j = md.length;
        char str[] = new char[j * 2];
        int k = 0;
        for (int i = 0; i < j; i++) {
            byte byte0 = md[i];
            str[k++] = hexDigits[byte0 >>> 4 & 0xf];
            str[k++] = hexDigits[byte0 & 0xf];
        }
        return new String(str);
    }
}
