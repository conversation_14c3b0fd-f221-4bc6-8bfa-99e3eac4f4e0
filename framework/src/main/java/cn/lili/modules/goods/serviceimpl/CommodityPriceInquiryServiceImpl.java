package cn.lili.modules.goods.serviceimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.StringUtils;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.entity.dos.CommodityPriceInquiry;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquiryQueryDto;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquirySubmitDto;
import cn.lili.modules.goods.entity.dto.GoodsSearchParams;
import cn.lili.modules.goods.entity.enums.CommodityPriceInquiryStatusEnum;
import cn.lili.modules.goods.entity.enums.GoodsAuthEnum;
import cn.lili.modules.goods.entity.enums.GoodsStatusEnum;
import cn.lili.modules.goods.entity.enums.SettlementModelEnum;
import cn.lili.modules.goods.entity.vos.CommodityPriceInquiryVo;
import cn.lili.modules.goods.mapper.CommodityPriceInquiryMapper;
import cn.lili.modules.goods.mapper.CommodityPriceQuoteMapper;
import cn.lili.modules.goods.service.GoodsService;
import cn.lili.modules.goods.service.ICommodityPriceInquiryService;
import cn.lili.modules.goods.util.ProductSearchExample;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品询价相关处理
 * <AUTHOR>
 * @date 2025/3/5
 */
@Service
@RequiredArgsConstructor
public class CommodityPriceInquiryServiceImpl extends ServiceImpl<CommodityPriceInquiryMapper, CommodityPriceInquiry> implements ICommodityPriceInquiryService {

    private final ProductSearchExample productSearchExample;

    private final GoodsService goodsService;
    private final CommodityPriceQuoteMapper commodityPriceQuoteMapper;
    private final CommodityPriceInquiryMapper commodityPriceInquiryMapper;

    /**
     * 询价提交
     * @param dto
     * @return
     */
    @Override
    public Boolean submit(CommodityPriceInquirySubmitDto dto) {
        // 判断下如果结算方式为定金, 则定金比例必传
        if (dto.getSettlementModel().equals(SettlementModelEnum.DEPOSIT_SEND_OUT_GOODS.getCode()) && ObjectUtil.isEmpty(dto.getEarnestMoneyRatio())) {
            throw new ServiceException("定金发货需填写定金比例");
        }
        AuthUser currentUser = UserContext.getCurrentUser();
        CommodityPriceInquiry commodityPriceInquiry = BeanUtil.copyProperties(dto, CommodityPriceInquiry.class);
        commodityPriceInquiry.setCustomerId(currentUser.getId());
        commodityPriceInquiry.setStatus(CommodityPriceInquiryStatusEnum.PRICE_INQUIRY.getCode());
        commodityPriceInquiry.setQuoteSupplierStoreIds("");
        return this.saveOrUpdate(commodityPriceInquiry);
    }

    /**
     * 根据供应商所属机构查询询价记录
     * @param query
     * @param storeId
     * @return
     */
    @Override
    public IPage<CommodityPriceInquiryVo> pageBySupplier(PageVO query, String storeId, CommodityPriceInquiryQueryDto dto) {
        String commodityListName = dto.getCommodityListName();
        // 询价记录未报价和已报价的数据
        List<Integer> inquiryStatusList = Arrays.asList(CommodityPriceInquiryStatusEnum.PRICE_INQUIRY.getCode(), CommodityPriceInquiryStatusEnum.PRICE_QUOTE.getCode());
        // 查询询价记录
        // 如果传入的商品名称不为空，则根据商品名称模糊查询询价记录
        List<CommodityPriceInquiry> inquiryList = this.list(Wrappers.<CommodityPriceInquiry>lambdaQuery()
                .in(CommodityPriceInquiry::getStatus, inquiryStatusList)
                // 去除掉自己的询价记录
                .ne(CommodityPriceInquiry::getCustomerId, UserContext.getCurrentUser().getId())
                .like(StringUtils.isNotBlank(commodityListName), CommodityPriceInquiry::getCommodityListName, commodityListName)
                // 去除小于当天的
                .apply("DATE_FORMAT(price_quote_end_date, '%Y-%m-%d') >= DATE_FORMAT({0}, '%Y-%m-%d')", LocalDate.now().toString())
        );
        // 如果查询到的询价记录是空的，则返回空
        if (CollectionUtil.isEmpty(inquiryList)) {
            return new Page<>(query.getPageNumber(), query.getPageSize());
        }
        // 获取当前登录店铺上架且审核通过的商品
        GoodsSearchParams goodsSearchParams = new GoodsSearchParams();
        goodsSearchParams.setStoreId(storeId);
        goodsSearchParams.setAuthFlag(GoodsAuthEnum.PASS.name());
        goodsSearchParams.setMarketEnable(GoodsStatusEnum.UPPER.name());
        List<Goods> goods = goodsService.queryListByParams(goodsSearchParams);
        // 如果供应商没有商品，则返回空
        if (CollectionUtil.isEmpty(goods)) {
            return new Page<>(query.getPageNumber(), query.getPageSize());
        }
        List<String> goodsIds = CollStreamUtil.toList(goods, Goods::getId);

        // 供应商需要展示的询价记录id
        List<String> myInquiryIdList = new ArrayList<>();
        // 根据询价记录中的商品名称进行分词，判断供应商的商品是否包含在询价记录中的商品名称中，剔除不包含的
        for (CommodityPriceInquiry commodityPriceInquiry : inquiryList) {
            // 商品id
            List<String> productIdStrs = productSearchExample.searchProductIdsByName(commodityPriceInquiry.getCommodityListName());
            for (String productIdStr : productIdStrs) {
                if (goodsIds.contains(productIdStr)) {
                    myInquiryIdList.add(commodityPriceInquiry.getId());
                }
            }
        }
        // 查询供应商的报价记录，变更对应报价状态
        List<String> distinct = myInquiryIdList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(distinct)) {
            return new Page<>(query.getPageNumber(), query.getPageSize());
        }
        dto = new CommodityPriceInquiryQueryDto();
        dto.setInquiryIds(distinct);
        IPage<CommodityPriceInquiryVo> page = this.pageByQueryDto(query, dto);
        for (CommodityPriceInquiryVo record : page.getRecords()) {
            String quoteSupplierDeptIds = record.getQuoteSupplierDeptIds();
            if (!StringUtils.containsAny(quoteSupplierDeptIds, storeId)) {
                record.setStatus(CommodityPriceInquiryStatusEnum.PRICE_INQUIRY.getCode());
            }
        }
        return page;
    }

    /**
     * 根据id变更询价记录状态
     * @param id
     * @param inquiryStatusEnum
     * @return
     */
    @Override
    public Boolean updateStatus(String id, CommodityPriceInquiryStatusEnum inquiryStatusEnum) {
        return this.update(Wrappers.<CommodityPriceInquiry>lambdaUpdate()
                .eq(CommodityPriceInquiry::getId, id)
                .set(CommodityPriceInquiry::getStatus, inquiryStatusEnum.getCode())
        );
    }

    /**
     * 根据id变更状态并设置报价机构id
     * @param id
     * @param inquiryStatusEnum
     * @return
     */
    @Override
    public Boolean updateStatusAndSetQuotaStoreId(Long id, CommodityPriceInquiryStatusEnum inquiryStatusEnum, String storeId) {
        CommodityPriceInquiry entity = this.getById(id);
        entity.setStatus(inquiryStatusEnum.getCode());
        String quoteSupplierStoreIds = entity.getQuoteSupplierStoreIds();
        if (StringUtils.isBlank(quoteSupplierStoreIds)) {
            quoteSupplierStoreIds = "";
        }
        if (!quoteSupplierStoreIds.contains(String.valueOf(storeId))) {
            quoteSupplierStoreIds = quoteSupplierStoreIds + storeId + ",";
            entity.setQuoteSupplierStoreIds(quoteSupplierStoreIds);
        }
        return this.updateById(entity);
    }

    @Override
    public IPage<CommodityPriceInquiryVo> pageByQueryDto(PageVO query, CommodityPriceInquiryQueryDto dto) {
        return commodityPriceInquiryMapper.pageByQueryDto(new Page<>(query.getPageNumber(), query.getPageSize()), dto);
    }

    /**
     * 根据订单号查询
     * @param orderSn
     * @return
     */
    @Override
    public CommodityPriceInquiryVo getByOrderSn(String orderSn) {
        return commodityPriceInquiryMapper.getByOrderSn(orderSn);
    }

}
