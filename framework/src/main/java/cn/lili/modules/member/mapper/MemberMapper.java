package cn.lili.modules.member.mapper;


import cn.lili.modules.member.entity.dos.Member;
import cn.lili.modules.member.entity.vo.MemberVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 会员数据处理层
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
public interface MemberMapper extends BaseMapper<Member> {

    /**
     * 获取所有的会员手机号
     * @return 会员手机号
     */
    @Select("select m.mobile from li_member m")
    List<String> getAllMemberMobile();

//    @Select("select * from li_member ${ew.customSqlSegment}")
    @Select("SELECT m.id, m.username, m.nick_name, m.sex, m.birthday, m.region_id, m.region, " +
            "m.mobile, m.point, m.total_point, m.face, m.disabled, m.have_store, m.store_id, " +
            "m.client_enum, m.last_login_date, m.grade_id, m.experience, m.status, " +
            "m.connect_sys_type, m.finance_account, m.create_time, " +
            "ci.corporation_name, ci.corp_name, ci.business_licence_number " +
            "FROM li_member m LEFT JOIN li_customer_info ci ON m.id = ci.customer_id " +
            "${ew.customSqlSegment}")
    IPage<MemberVO> pageByMemberVO(IPage<MemberVO> page, @Param(Constants.WRAPPER) Wrapper<Member> queryWrapper);
}